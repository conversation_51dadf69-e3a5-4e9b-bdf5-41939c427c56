<?php

namespace Tests\Unit;

use App\Enums\TicketCategorySlaFrequencyEnum;
use App\Enums\TicketStatusEnum;
use App\Models\Operator;
use App\Models\Team;
use App\Models\Ticket;
use App\Models\TicketCategory;
use App\Models\User;
use Tests\TestCase;

class TicketSlaTest extends TestCase
{
    protected User $user;
    protected Operator $operator;
    protected Team $team;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and operator
        $this->user = User::factory()->create();
        $this->operator = Operator::create([
            'user_id' => $this->user->id,
            'name' => $this->user->name,
            'email' => $this->user->email,
        ]);

        // Create test team
        $this->team = Team::create([
            'name' => 'Test Team',
            'leader_id' => $this->operator->id
        ]);

        $this->actingAs($this->user);
    }

    public function test_sla_status_returns_finalizado_for_resolved_ticket()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 2,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Resolved->value,
            'resolved_at' => now(),
            'resolved_by_user_id' => $this->user->id,
        ]);

        $this->assertEquals('finalizado', $ticket->sla_status);
    }

    public function test_sla_status_returns_finalizado_for_cancelled_ticket()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 2,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Cancelled->value,
            'cancelled_at' => now(),
            'cancelled_by_user_id' => $this->user->id,
        ]);

        $this->assertEquals('finalizado', $ticket->sla_status);
    }

    public function test_sla_status_returns_no_prazo_for_ticket_within_sla()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 2,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        // Create ticket and set created_at to 1 hour ago (within 2-hour SLA)
        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Pending->value,
        ]);

        // Manually update the created_at timestamp
        $ticket->timestamps = false;
        $ticket->created_at = now()->subHour();
        $ticket->save();
        $ticket->timestamps = true;

        $this->assertEquals('no prazo', $ticket->sla_status);
    }

    public function test_sla_status_returns_atrasado_for_ticket_past_sla()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 1,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        // Create ticket and then manually set created_at to 2 hours ago (past 1-hour SLA)
        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Pending->value,
        ]);

        // Manually update the created_at timestamp
        $ticket->timestamps = false;
        $ticket->created_at = now()->subHours(2);
        $ticket->save();
        $ticket->timestamps = true;

        $this->assertEquals('atrasado', $ticket->sla_status);
    }

    public function test_sla_status_with_minute_frequency()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 30,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Minute->value,
            'term' => 'Test term'
        ]);

        // Create ticket and set created_at to 45 minutes ago (past 30-minute SLA)
        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Ongoing->value,
        ]);

        // Manually update the created_at timestamp
        $ticket->timestamps = false;
        $ticket->created_at = now()->subMinutes(45);
        $ticket->save();
        $ticket->timestamps = true;

        $this->assertEquals('atrasado', $ticket->sla_status);
    }

    public function test_sla_status_with_day_frequency()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 3,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Day->value,
            'term' => 'Test term'
        ]);

        // Create ticket and set created_at to 2 days ago (within 3-day SLA)
        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Ongoing->value,
        ]);

        // Manually update the created_at timestamp
        $ticket->timestamps = false;
        $ticket->created_at = now()->subDays(2);
        $ticket->save();
        $ticket->timestamps = true;

        $this->assertEquals('no prazo', $ticket->sla_status);
    }

    public function test_sla_status_returns_no_prazo_when_no_sla_configuration()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 0, // Use 0 instead of null to simulate no SLA
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Pending->value,
        ]);

        $this->assertEquals('no prazo', $ticket->sla_status);
    }

    public function test_sla_status_badge_returns_correct_html_for_finalizado()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 2,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Resolved->value,
            'resolved_at' => now(),
            'resolved_by_user_id' => $this->user->id,
        ]);

        $expectedBadge = '<span class="badge badge-secondary px-2 py-1">Finalizado</span>';
        $this->assertEquals($expectedBadge, $ticket->sla_status_badge);
    }

    public function test_sla_status_badge_returns_correct_html_for_atrasado()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 1,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Pending->value,
        ]);

        // Manually update the created_at timestamp
        $ticket->timestamps = false;
        $ticket->created_at = now()->subHours(2);
        $ticket->save();
        $ticket->timestamps = true;

        $expectedBadge = '<span class="badge badge-danger px-2 py-1">Atrasado</span>';
        $this->assertEquals($expectedBadge, $ticket->sla_status_badge);
    }

    public function test_sla_status_badge_returns_correct_html_for_no_prazo()
    {
        $ticketCategory = TicketCategory::create([
            'team_id' => $this->team->id,
            'name' => 'Test Category',
            'priority' => 1,
            'sla_quantity' => 2,
            'sla_frequency' => TicketCategorySlaFrequencyEnum::Hour->value,
            'term' => 'Test term'
        ]);

        $ticket = Ticket::create([
            'ticket_category_id' => $ticketCategory->id,
            'assigned_to_user_id' => $this->user->id,
            'team_id' => $this->team->id,
            'summary' => 'Test ticket',
            'content' => 'Test content',
            'status' => TicketStatusEnum::Pending->value,
        ]);

        // Manually update the created_at timestamp
        $ticket->timestamps = false;
        $ticket->created_at = now()->subMinutes(30);
        $ticket->save();
        $ticket->timestamps = true;

        $expectedBadge = '<span class="badge badge-success px-2 py-1">No Prazo</span>';
        $this->assertEquals($expectedBadge, $ticket->sla_status_badge);
    }
}
