<?php

namespace App\Http\Livewire\Ticket\Tables;

use App\Core\Http\Livewire\LaravelLivewireTables\BaseLivewireTable;
use App\Enums\TicketCategoryPriorityEnum;
use App\Enums\TicketStatusEnum;
use App\Models\Role;
use App\Models\Ticket;
use Illuminate\Database\Eloquent\Builder;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class TicketsTable extends BaseLivewireTable
{
    protected $model = Ticket::class;
    protected string $resourceRoute = 'tickets';

    public array $ticketCategories;
    public array $teams;

    public function mount(array $ticketCategories, array $teams): void
    {
        $this->ticketCategories = $ticketCategories;
        $this->teams = $teams;
    }

    public function configure(): void
    {
        parent::configure();

        $this->setDefaultSort('id', 'desc');

        $this->setTrAttributes(function (Ticket $row) {
            return [
                'class' => $row->sla_status === 'atrasado' ? 'bg-danger-light' : '',
            ];
        });
    }

    public function builder(): Builder
    {
        return Ticket::query()
            ->when(!auth()->user()->hasRole(Role::ADMINISTRATOR), function (Builder $query): Builder {
                return $query
                    ->where('created_by_user_id', auth()->id())
                    ->orWhere('assigned_to_user_id', auth()->id());
            });
    }

    public function columns(): array
    {
        return [
            Column::make(__('tickets.forms.fields.id'), 'id')
                ->sortable()
                ->searchable(),
            Column::make('ticket_category_id', 'ticket_category_id')
                ->hideIf(true),
            Column::make(__('ticket_categories.forms.fields.priority'), 'ticketCategory.priority')
                ->sortable()
                ->format(fn(string $state, Ticket $row): string => $row->ticketCategory->priority_badge)
                ->html(),
            Column::make(__('tickets.forms.fields.summary'), 'summary')
                ->sortable()
                ->searchable(),
            Column::make(__('tickets.forms.fields.ticket_category_id'), 'ticketCategory.name')
                ->sortable(),
            Column::make(__('tickets.forms.fields.team_id'), 'team.name')
                ->sortable(),
            Column::make(__('tickets.forms.fields.assigned_to_user_id'), 'assignedUser.name')
                ->sortable()
                ->searchable(),
            Column::make(__('tickets.forms.fields.status'), 'status')
                ->sortable()
                ->format(fn(string $state, Ticket $row): string => $row->friendly_status_badge)
                ->html(),
            Column::make(__('tickets.forms.fields.created_at'), 'created_at')
                ->sortable()
                ->format(fn(string $state, Ticket $row): string => format_date($row->created_at)),
            $this->getDefaultButtonLinkColumn(
                edit: false,
                overridePermission: true,
            ),
        ];
    }

    public function filters(): array
    {
        return [
            SelectFilter::make(__('ticket_categories.forms.fields.priority'), 'priority')
                ->setFilterPillTitle(__('ticket_categories.forms.fields.priority'))
                ->options(['' => 'Todas'] + TicketCategoryPriorityEnum::getTranslated())
                ->filter(fn(Builder $builder, string $value): Builder => $builder->where('ticket_categories.priority', $value)),
            SelectFilter::make(__('tickets.forms.fields.ticket_category_id'), 'ticket_category_id')
                ->setFilterPillTitle(__('tickets.forms.fields.ticket_category_id'))
                ->options(['' => 'Todas'] + $this->ticketCategories)
                ->filter(fn(Builder $builder, string $value): Builder => $builder->where('tickets.ticket_category_id', $value)),
            SelectFilter::make(__('tickets.forms.fields.team_id'), 'team_id')
                ->setFilterPillTitle(__('tickets.forms.fields.team_id'))
                ->options(['' => 'Todos'] + $this->teams)
                ->filter(fn(Builder $builder, string $value): Builder => $builder->where('tickets.team_id', $value)),
            SelectFilter::make(__('tickets.forms.fields.status'), 'status')
                ->setFilterPillTitle(__('tickets.forms.fields.status'))
                ->options(array_merge(['' => 'Todos'], TicketStatusEnum::getTranslated()))
                ->filter(fn(Builder $builder, string $value): Builder => $builder->where('tickets.status', $value)),
        ];
    }
}
