<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\Ticket\HandlesTicketAttributes;
use App\Models\Concerns\Ticket\HandlesTicketRelationships;

/**
 * Ticket model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $ticket_category_id
 * @property  int $created_by_user_id
 * @property  int $assigned_to_user_id
 * @property  int $team_id
 * @property  string $summary
 * @property  string $content
 * @property  string $status
 * @property  int $first_visualization_user_id
 * @property  \Carbon\Carbon $first_viewed_at
 * @property  \Carbon\Carbon $set_ongoing_at
 * @property  \Carbon\Carbon $resolved_at
 * @property  int $resolved_by_user_id
 * @property  \Carbon\Carbon $cancelled_at
 * @property  int $cancelled_by_user_id
 * @property  string $cancellation_reason_id
 * @property  string $cancellation_additional_info
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_status
 * @property  string $friendly_status_badge
 * @property  string $sla_status
 * @property  string $sla_status_badge
 *
 * @property  \App\Models\TicketCategory $ticketCategory
 * @property  \App\Models\User $creationUser
 * @property  \App\Models\User $assignedUser
 * @property  \App\Models\Team $team
 * @property  \App\Models\User $firstVisualizationUser
 * @property  \App\Models\User $resolutionUser
 * @property  \App\Models\User $cancellationUser
 * @property  \App\Models\Reason $cancellationReason
 *
 * @property  \Illuminate\Support\Collection|\App\Models\TicketReply[] $ticketReplies
 * @property  \Illuminate\Support\Collection|\App\Models\TicketNote[] $ticketNotes
 */
class Ticket extends Model
{
    use HandlesTicketAttributes;
    use HandlesTicketRelationships;

    public const MODULE = Module::TICKETS;
    public const RESOURCE_ROUTE = 'tickets';

    protected $fillable = [
        'ticket_category_id',
        'created_by_user_id',
        'assigned_to_user_id',
        'team_id',
        'summary',
        'content',
        'status',
        'first_visualization_user_id',
        'first_viewed_at',
        'set_ongoing_at',
        'resolved_at',
        'resolved_by_user_id',
        'cancelled_at',
        'cancelled_by_user_id',
        'cancellation_reason_id',
        'cancellation_additional_info',
    ];

    protected $casts = [
        'created_by_user_id' => 'int',
    ];

    protected $appends = [
        'friendly_status',
        'sla_status',
    ];

    public static function booted(): void
    {
        static::creating(function (self $ticket): void {
            $ticket->created_by_user_id = auth()->id();
        });
    }
}
