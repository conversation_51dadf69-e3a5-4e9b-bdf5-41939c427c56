<?php

namespace App\Models\Concerns\Ticket;

use App\Models\Reason;
use App\Models\Team;
use App\Models\TicketCategory;
use App\Models\TicketFile;
use App\Models\TicketNote;
use App\Models\TicketReply;
use App\Models\TicketUserTransfer;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesTicketRelationships
{
    public function ticketCategory(): BelongsTo
    {
        return $this->belongsTo(TicketCategory::class);
    }

    public function creationUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id');
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function firstVisualizationUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'first_visualization_user_id');
    }

    public function resolutionUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by_user_id');
    }

    public function cancellationUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by_user_id');
    }

    public function cancellationReason(): BelongsTo
    {
        return $this->belongsTo(Reason::class, 'cancellation_reason_id');
    }

    public function ticketReplies(): HasMany
    {
        return $this->hasMany(TicketReply::class);
    }

    public function ticketNotes(): HasMany
    {
        return $this->hasMany(TicketNote::class);
    }

    public function ticketFiles(): HasMany
    {
        return $this->hasMany(TicketFile::class);
    }

    public function ticketUserTransfers(): HasMany
    {
        return $this->hasMany(TicketUserTransfer::class);
    }
}
