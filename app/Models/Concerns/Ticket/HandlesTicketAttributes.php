<?php

namespace App\Models\Concerns\Ticket;

use App\Enums\TicketCategorySlaFrequencyEnum;
use App\Enums\TicketStatusEnum;
use Carbon\Carbon;

trait HandlesTicketAttributes
{
    public function getFriendlyStatusAttribute(): string
    {
        return TicketStatusEnum::getTranslated()[$this->status];
    }

    public function getFriendlyStatusBadgeAttribute(): string
    {
        return match ($this->status) {
            TicketStatusEnum::Ongoing->value => '<span class="badge badge-primary px-2 py-1">' . $this->friendly_status . '</span>',
            TicketStatusEnum::Resolved->value => '<span class="badge badge-success px-2 py-1">' . $this->friendly_status . '</span>',
            TicketStatusEnum::Cancelled->value => '<span class="badge badge-secondary px-2 py-1">' . $this->friendly_status . '</span>',
            default => '<span class="badge badge-warning px-2 py-1">' . $this->friendly_status . '</span>',
        };
    }

    public function getSlaStatusAttribute(): string
    {
        // If ticket is resolved or cancelled, SLA is finished
        if (in_array($this->status, [TicketStatusEnum::Resolved->value, TicketStatusEnum::Cancelled->value])) {
            return 'finalizado';
        }

        // If no ticket category or SLA configuration, return default
        if (!$this->ticketCategory || $this->ticketCategory->sla_quantity <= 0 || !$this->ticketCategory->sla_frequency) {
            return 'no prazo';
        }

        $slaDeadline = $this->calculateSlaDeadline();

        if (!$slaDeadline) {
            return 'no prazo';
        }

        return now()->isAfter($slaDeadline) ? 'atrasado' : 'no prazo';
    }

    public function getSlaStatusBadgeAttribute(): string
    {
        $slaStatus = $this->sla_status;

        return match ($slaStatus) {
            'finalizado' => '<span class="badge badge-secondary px-2 py-1">Finalizado</span>',
            'atrasado' => '<span class="badge badge-danger px-2 py-1">Atrasado</span>',
            'no prazo' => '<span class="badge badge-success px-2 py-1">No Prazo</span>',
            default => '<span class="badge badge-warning px-2 py-1">Indefinido</span>',
        };
    }

    public function getSlaDeadlineAttribute(): ?Carbon
    {
        return $this->calculateSlaDeadline();
    }

    /**
     * Calculate the SLA deadline based on ticket creation date and category SLA configuration.
     *
     * @return \Carbon\Carbon|null
     */
    protected function calculateSlaDeadline(): ?Carbon
    {
        if (!$this->ticketCategory || $this->ticketCategory->sla_quantity <= 0 || !$this->ticketCategory->sla_frequency) {
            return null;
        }

        $createdAt = carbon($this->created_at)->copy(); // Use copy() to avoid modifying the original
        $slaQuantity = $this->ticketCategory->sla_quantity;
        $slaFrequency = $this->ticketCategory->sla_frequency;

        return match ($slaFrequency) {
            TicketCategorySlaFrequencyEnum::Minute->value => $createdAt->addMinutes($slaQuantity),
            TicketCategorySlaFrequencyEnum::Hour->value => $createdAt->addHours($slaQuantity),
            TicketCategorySlaFrequencyEnum::Day->value => $createdAt->addDays($slaQuantity),
            default => null,
        };
    }
}
