<?php

namespace App\Models;

use App\Models\Concerns\TicketNote\HandlesTicketNoteRelationships;

/**
 * Ticket note model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $ticket_id
 * @property  int $user_id
 * @property  string $message
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Ticket $ticket
 * @property  \App\Models\User $user
 */
class TicketNote extends Model
{
    use HandlesTicketNoteRelationships;

    protected $fillable = [
        'ticket_id',
        'user_id',
        'message',
    ];

    protected $casts = [
        'ticket_id' => 'int',
        'user_id' => 'int',
    ];

    protected static function booted()
    {
        static::saving(function (self $ticketNote): void {
            $ticketNote->user_id = auth()->id();
        });
    }
}
