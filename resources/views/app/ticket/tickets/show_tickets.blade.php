@extends('layouts.app')

@section('actions')
    <x-button.back :route="route('tickets.index')" />
@endsection

@section('content')
    <x-card :title="__('tickets.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('tickets.index')" :actions="true">
        <x-slot name="badges">
            <div>
                @if ($ticket->status === \App\Enums\TicketStatusEnum::Cancelled->value)
                    <span class="badge badge-secondary px-2 py-1">{{ $ticket->friendly_status }}</span>
                @elseif ($ticket->status === \App\Enums\TicketStatusEnum::Ongoing->value)
                    <span class="badge badge-primary px-2 py-1">{{ $ticket->friendly_status }}</span>
                @elseif ($ticket->status === \App\Enums\TicketStatusEnum::Resolved->value)
                    <span class="badge badge-success px-2 py-1">{{ $ticket->friendly_status }}</span>
                @else
                    <span class="badge badge-warning px-2 py-1">{{ $ticket->friendly_status }}</span>
                @endif
            </div>
        </x-slot>

        <x-slot name="actionLinks">
            @if (in_array($ticket->status, [\App\Enums\TicketStatusEnum::Ongoing->value]))
                <x-card.actions.modal-action-link modalId="reply" text="Responder" />
                <x-card.actions.modal-action-link modalId="annotate" text="Anotar" />
                <x-card.actions.modal-action-link modalId="resolve" text="Marcar como concluído" />
                @if ($ticket->created_by_user_id === auth()->id())
                    <x-card.actions.modal-action-link modalId="cancel" text="Cancelar" />
                @endif
            @elseif (in_array($ticket->status, [\App\Enums\TicketStatusEnum::Pending->value]))
                <x-card.actions.modal-action-link modalId="set-ongoing" text="Iniciar atendimento" />
            @endif
            @if (!in_array($ticket->status, [\App\Enums\TicketStatusEnum::Resolved->value, \App\Enums\TicketStatusEnum::Cancelled->value]))
                <x-card.actions.modal-action-link modalId="transfer" text="Transferir para outro operador" />
            @endif
        </x-slot>

        <x-form.section>
            <strong class="card-header-title">Ticket #{{ $ticket->id }}</strong>
            <p>{{ $ticket->summary }}</p>
        </x-form.section>

        <x-form.section>
            <x-form.row>
                <div class="col-12">
                    <div>
                        <span class="text-black-50 small">{{ \Carbon\Carbon::parse($ticket->created_at)->setTimezone($account->default_timezone ?? '-3:00')->format('d/m/Y H:i:s') }}</span>
                    </div>
                    <div>
                        <span class="text-black-50 small">{{ ($ticket->creationUser->name ?? $ticket->operator_name) . ' ' . __('general.expressions.opened_a_ticket') }}:</span>
                    </div>
                    {!! $ticket->content !!}
                </div>
            </x-form.row>
            @if ($ticket->ticketFiles->count() > 0)
                <x-form.row>
                    <div class="col-12">
                        <strong>Anexos:</strong>
                    </div>
                </x-form.row>
                <x-form.row>
                    @foreach ($ticket->ticketFiles as $file)
                        <div class="col-12">
                            <a href="{{ $file->download_link }}" target="_blank">{{ $file->filename }}</a>
                        </div>
                    @endforeach
                </x-form.row>
            @endif
        </x-form.section>

        @foreach ($timelineItems as $item)
            @if (get_class($item) === \App\Models\TicketReply::class)
                <x-form.section>
                    <x-form.row>
                        <div class="col-12">
                            <div>
                                <span class="text-black-50 small">{{ \Carbon\Carbon::parse($item['created_at'])->setTimezone($account->default_timezone ?? '-3:00')->format('d/m/Y H:i:s') }}</span>
                            </div>
                            <div>
                                <span class="text-black-50 small">{{ $item->user->name . ' ' . __('general.expressions.answered') }}:</span>
                            </div>
                            <div class="trix-content">
                                {!! $item->message !!}
                            </div>
                        </div>
                    </x-form.row>
                    @if ($item->ticketReplyFiles->count() > 0)
                        <x-form.row>
                            <div class="col-12">
                                <strong>Anexos:</strong>
                            </div>
                        </x-form.row>
                        <x-form.row>
                            @foreach ($item->ticketReplyFiles as $file)
                                <div class="col-12">
                                    <a href="{{ $file->download_link }}" target="_blank">{{ $file->filename }}</a>
                                </div>
                            @endforeach
                        </x-form.row>
                    @endif
                </x-form.section>
            @elseif (auth()->id() === $ticket->assigned_to_user_id || auth()->user()->hasRole(\App\Models\Role::ADMINISTRATOR))
                <x-form.section>
                    <div>
                        <div class="col-12">
                            <div>
                                <span class="text-black-50 small">{{ \Carbon\Carbon::parse($item['created_at'])->setTimezone($account->default_timezone ?? '-3:00')->format('d/m/Y H:i:s') }}</span>
                            </div>
                            <div>
                                <span class="text-black-50 small">{{ $item->user->name . ' ' . __('general.expressions.annotated') }}:</span>
                            </div>
                            <div class="trix-content">
                                {!! $item->message !!}
                            </div>
                        </div>
                    </div>
                </x-form.section>
            @endif
        @endforeach
    </x-card>

    <x-modal.default-action id="reply" title="Responder" :route="route('ticket_replies.store', $ticket->id)" :hasFile="true">
        <x-form.row>
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <label class="mb-1" for="reply_message">{{ __('tickets.cards.show.body.fields.reply_message') }}<span class="text-danger"><strong>*</strong></span></label>
                </div>
                <div id="reply_message" class="form-control contents" style="overflow-y: auto; height: 200px"></div>
            </div>
        </x-form.row>
        <x-form.section title="Anexos" :marginTop="true">
            <x-form.row>
                <div class="mr-auto pl-3">
                    <button id="addFileBtn" class="btn btn-primary btn-sm shadow-sm" type="button"><i class="fa fa-plus"></i></button>
                    <button id="removeFileBtn" class="btn btn-danger btn-sm shadow-sm" type="button"><i class="fa fa-minus"></i></button>
                </div>
            </x-form.row>
            <x-form.row :marginTop="true" customClass="file-group">
                <div class="col-12 file-group-item">
                    <input type="file" name="files[]" class="input-group control-group form-control file-input mt-2">
                </div>
            </x-form.row>
        </x-form.section>
    </x-modal.default-action>

    <x-modal.default-action id="annotate" title="Anotar" :route="route('ticket_notes.store', $ticket->id)">
        <x-form.row>
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <label class="mb-1" for="annotate_message">{{ __('tickets.cards.show.body.fields.annotate_message') }}<span class="text-danger"><strong>*</strong></span></label>
                </div>
                <div id="annotate_message" class="form-control contents" style="overflow-y: auto; height: 200px"></div>
            </div>
        </x-form.row>
    </x-modal.default-action>

    <x-modal.default-action id="set-ongoing" title="Iniciar atendimento" :route="route('tickets.set_ongoing', $ticket->id)" confirmationMessage="Deseja iniciar o atendimento do chamado?" />
    <x-modal.default-action id="resolve" title="Iniciar atendimento" :route="route('tickets.resolve', $ticket->id)" confirmationMessage="Deseja marcar o chamado como concluído?" />

    <x-modal.default-action id="transfer" title="Transferir para outro operador" :route="route('tickets.transfer', $ticket->id)" size="lg">
        <x-form.row>
            <x-input.select :$resourceRoute :$action field="user_id" :required="true">
                @foreach ($users as $key => $value)
                    <option value="{{ $key }}">{{ $value }}</option>
                @endforeach
            </x-input.select>
        </x-form.row>
        <x-form.row :marginTop="true">
            <x-input.textarea :$resourceRoute :$action field="additional_info" :required="true" :rows="5" />
        </x-form.row>
    </x-modal.default-action>

    <x-modal.default-action id="cancel" title="Cancelar" :route="route('tickets.cancel', $ticket->id)">
        <p>O chamado será cancelado e o atendimento será finalizado com o mesmo status.</p>
        <p>Para que o atendimento seja continuado, será necessário criar um novo chamado.</p>
        <p><strong>Você tem certeza que deseja cancelar o chamado?</strong></p>
        <x-form.row :marginTop="true">
            <x-input.select :$resourceRoute :$action field="cancellation_reason_id">
                @foreach ($cancellationReasons as $cancellationReason)
                    <option value="{{ $cancellationReason->id }}">{{ $cancellationReason->name }}</option>
                @endforeach
            </x-input.select>
        </x-form.row>
        <x-form.row :marginTop="true">
            <x-input.textarea :$resourceRoute :$action field="cancellation_additional_info" />
        </x-form.row>
    </x-modal.default-action>
@endsection

@section('js-scripts')
<script src="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.js"></script>
<script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>

<script>
    $(document).ready(function() {
        var toolbarOptions = [
            ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
            ['blockquote'],

            [{ 'header': 1 }, { 'header': 2 }],               // custom button values
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
            [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent

            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [ 'link', 'image' ],          // add's image support
            [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
            [{ 'align': [] }],
        ];

        let quill = new Quill("#reply_message", {
            modules: {
                toolbar: toolbarOptions
            },
            theme: 'snow'
        });

        quill = new Quill("#annotate_message", {
            modules: {
                toolbar: toolbarOptions
            },
            theme: 'snow'
        });

        $('#addFileBtn').click(function() {
            $('.file-group').append($('.file-group-item').last().clone());
            $('.input-group.control-group').last().val(null);
        });

        $('#removeFileBtn').click(function() {
            const fileGroupItemSelector = $('.file-group-item');

            if (fileGroupItemSelector.length > 1) {
                fileGroupItemSelector.last().remove();
            } else {
                $('.input-group.control-group').val(null);
            }
        });

        $('#reply-form').submit(function () {
            Array.from($('.contents')).forEach(content => {
                $('#reply-form').append(
                    $('<input>', {
                        type: 'hidden',
                        name: "message",
                        value: $("#reply_message")[0].children[0].innerHTML
                    })
                );
            });
        });

        $('#annotate-form').submit(function () {
            Array.from($('.contents')).forEach(content => {
                $('#annotate-form').append(
                    $('<input>', {
                        type: 'hidden',
                        name: "message",
                        value: $("#annotate_message")[0].children[0].innerHTML
                    })
                );
            });
        });
    });
</script>
@endsection
